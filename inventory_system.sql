-- 出入库管理系统表结构设计
-- 使用MySQL计算列和JSON字段实现

-- 创建数据库
CREATE DATABASE IF NOT EXISTS inventory_system 
CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE inventory_system;

-- 库存管理主表
CREATE TABLE inventory (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    product_code VARCHAR(50) NOT NULL UNIQUE COMMENT '商品编码',
    product_name VARCHAR(200) NOT NULL COMMENT '商品名称',
    unit VARCHAR(20) NOT NULL DEFAULT '件' COMMENT '单位',

    -- JSON字段存储出入库记录
    transactions JSON NOT NULL DEFAULT '[]' COMMENT '出入库记录JSON数组',

    -- 手动维护的库存字段（通过应用层逻辑更新）
    current_stock DECIMAL(10,2) NOT NULL DEFAULT 0 COMMENT '当前库存数量',
    total_in DECIMAL(10,2) NOT NULL DEFAULT 0 COMMENT '总入库数量',
    total_out DECIMAL(10,2) NOT NULL DEFAULT 0 COMMENT '总出库数量',
    
    -- 基础字段
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    -- 约束：库存不能为负数
    CONSTRAINT chk_current_stock CHECK (current_stock >= 0),
    CONSTRAINT chk_total_in CHECK (total_in >= 0),
    CONSTRAINT chk_total_out CHECK (total_out >= 0),

    -- 索引
    INDEX idx_product_code (product_code),
    INDEX idx_current_stock (current_stock),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB COMMENT='库存管理表';

-- 创建视图：库存明细查询
CREATE VIEW v_inventory_detail AS
SELECT 
    i.id,
    i.product_code,
    i.product_name,
    i.unit,
    i.current_stock,
    i.total_in,
    i.total_out,
    -- 解析JSON获取最近的交易记录
    JSON_EXTRACT(i.transactions, '$[last]') as last_transaction,
    i.created_at,
    i.updated_at
FROM inventory i;

-- 创建视图：出入库记录明细
CREATE VIEW v_transaction_detail AS
SELECT 
    i.id as inventory_id,
    i.product_code,
    i.product_name,
    jt.transaction_id,
    jt.type,
    jt.quantity,
    jt.price,
    jt.total_amount,
    jt.operator,
    jt.remark,
    jt.transaction_time
FROM inventory i
CROSS JOIN JSON_TABLE(
    i.transactions, 
    '$[*]' COLUMNS (
        transaction_id VARCHAR(50) PATH '$.transaction_id',
        type VARCHAR(10) PATH '$.type',
        quantity DECIMAL(10,2) PATH '$.quantity',
        price DECIMAL(10,2) PATH '$.price',
        total_amount DECIMAL(10,2) PATH '$.total_amount',
        operator VARCHAR(50) PATH '$.operator',
        remark VARCHAR(500) PATH '$.remark',
        transaction_time DATETIME PATH '$.transaction_time'
    )
) AS jt
ORDER BY jt.transaction_time DESC;

-- 存储过程：入库操作
DELIMITER //
CREATE PROCEDURE sp_stock_in(
    IN p_product_code VARCHAR(50),
    IN p_quantity DECIMAL(10,2),
    IN p_price DECIMAL(10,2),
    IN p_operator VARCHAR(50),
    IN p_remark VARCHAR(500)
)
BEGIN
    DECLARE v_transaction_id VARCHAR(50);
    DECLARE v_total_amount DECIMAL(10,2);

    -- 生成交易ID
    SET v_transaction_id = CONCAT('IN', DATE_FORMAT(NOW(), '%Y%m%d%H%i%s'), LPAD(CONNECTION_ID(), 4, '0'));
    SET v_total_amount = p_quantity * p_price;

    -- 更新库存和添加交易记录
    UPDATE inventory
    SET
        transactions = JSON_ARRAY_APPEND(
            transactions,
            '$',
            JSON_OBJECT(
                'transaction_id', v_transaction_id,
                'type', 'in',
                'quantity', p_quantity,
                'price', p_price,
                'total_amount', v_total_amount,
                'operator', p_operator,
                'remark', p_remark,
                'transaction_time', NOW()
            )
        ),
        current_stock = current_stock + p_quantity,
        total_in = total_in + p_quantity,
        updated_at = NOW()
    WHERE product_code = p_product_code;

    -- 返回操作结果
    SELECT
        v_transaction_id as transaction_id,
        p_product_code as product_code,
        'SUCCESS' as status,
        current_stock as new_stock
    FROM inventory
    WHERE product_code = p_product_code;
END //

-- 存储过程：出库操作
CREATE PROCEDURE sp_stock_out(
    IN p_product_code VARCHAR(50),
    IN p_quantity DECIMAL(10,2),
    IN p_price DECIMAL(10,2),
    IN p_operator VARCHAR(50),
    IN p_remark VARCHAR(500)
)
BEGIN
    DECLARE v_transaction_id VARCHAR(50);
    DECLARE v_total_amount DECIMAL(10,2);
    DECLARE v_current_stock DECIMAL(10,2);
    DECLARE v_affected_rows INT DEFAULT 0;

    -- 检查当前库存
    SELECT current_stock INTO v_current_stock
    FROM inventory
    WHERE product_code = p_product_code;

    -- 库存不足检查
    IF v_current_stock < p_quantity THEN
        SELECT
            '' as transaction_id,
            p_product_code as product_code,
            'INSUFFICIENT_STOCK' as status,
            v_current_stock as current_stock,
            p_quantity as required_quantity;
    ELSE
        -- 生成交易ID
        SET v_transaction_id = CONCAT('OUT', DATE_FORMAT(NOW(), '%Y%m%d%H%i%s'), LPAD(CONNECTION_ID(), 4, '0'));
        SET v_total_amount = p_quantity * p_price;

        -- 更新库存和添加交易记录
        UPDATE inventory
        SET
            transactions = JSON_ARRAY_APPEND(
                transactions,
                '$',
                JSON_OBJECT(
                    'transaction_id', v_transaction_id,
                    'type', 'out',
                    'quantity', p_quantity,
                    'price', p_price,
                    'total_amount', v_total_amount,
                    'operator', p_operator,
                    'remark', p_remark,
                    'transaction_time', NOW()
                )
            ),
            current_stock = current_stock - p_quantity,
            total_out = total_out + p_quantity,
            updated_at = NOW()
        WHERE product_code = p_product_code
          AND current_stock >= p_quantity;

        SET v_affected_rows = ROW_COUNT();

        -- 返回操作结果
        IF v_affected_rows > 0 THEN
            SELECT
                v_transaction_id as transaction_id,
                p_product_code as product_code,
                'SUCCESS' as status,
                current_stock as new_stock
            FROM inventory
            WHERE product_code = p_product_code;
        ELSE
            SELECT
                '' as transaction_id,
                p_product_code as product_code,
                'FAILED' as status,
                current_stock as current_stock
            FROM inventory
            WHERE product_code = p_product_code;
        END IF;
    END IF;
END //
DELIMITER ;

-- 示例数据插入
-- 1. 创建商品并初始化库存
INSERT INTO inventory (product_code, product_name, unit) VALUES
('P001', '苹果手机', '台'),
('P002', '笔记本电脑', '台'),
('P003', '无线耳机', '个');

-- 2. 入库操作示例
CALL sp_stock_in('P001', 100, 5000.00, '张三', '首次入库');
CALL sp_stock_in('P002', 50, 8000.00, '张三', '笔记本入库');
CALL sp_stock_in('P003', 200, 300.00, '李四', '耳机入库');

-- 3. 出库操作示例
CALL sp_stock_out('P001', 10, 5200.00, '王五', '销售出库');
CALL sp_stock_out('P002', 5, 8500.00, '王五', '销售出库');

-- 4. 尝试超量出库（应该失败）
CALL sp_stock_out('P001', 200, 5200.00, '赵六', '尝试超量出库');

-- 函数：获取商品库存
DELIMITER //
CREATE FUNCTION fn_get_stock(p_product_code VARCHAR(50))
RETURNS DECIMAL(10,2)
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE v_stock DECIMAL(10,2) DEFAULT 0;

    SELECT current_stock INTO v_stock
    FROM inventory
    WHERE product_code = p_product_code;

    RETURN COALESCE(v_stock, 0);
END //

-- 函数：检查是否可以出库
CREATE FUNCTION fn_can_stock_out(p_product_code VARCHAR(50), p_quantity DECIMAL(10,2))
RETURNS BOOLEAN
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE v_stock DECIMAL(10,2) DEFAULT 0;

    SELECT current_stock INTO v_stock
    FROM inventory
    WHERE product_code = p_product_code;

    RETURN COALESCE(v_stock, 0) >= p_quantity;
END //
DELIMITER ;

-- 查询示例
-- 1. 查看所有商品库存
SELECT 
    product_code,
    product_name,
    current_stock,
    total_in,
    total_out,
    unit
FROM inventory
ORDER BY current_stock DESC;

-- 2. 查看库存不足的商品（库存小于10）
SELECT 
    product_code,
    product_name,
    current_stock,
    unit
FROM inventory
WHERE current_stock < 10
ORDER BY current_stock ASC;

-- 3. 查看某商品的所有交易记录
SELECT 
    product_code,
    product_name,
    transaction_id,
    type,
    quantity,
    price,
    total_amount,
    operator,
    remark,
    transaction_time
FROM v_transaction_detail
WHERE product_code = 'P001'
ORDER BY transaction_time DESC;

-- 4. 查看今日交易汇总
SELECT
    DATE(jt.transaction_time) as transaction_date,
    jt.type,
    COUNT(*) as transaction_count,
    SUM(jt.quantity) as total_quantity,
    SUM(jt.total_amount) as total_amount
FROM inventory i
CROSS JOIN JSON_TABLE(
    i.transactions,
    '$[*]' COLUMNS (
        type VARCHAR(10) PATH '$.type',
        quantity DECIMAL(10,2) PATH '$.quantity',
        total_amount DECIMAL(10,2) PATH '$.total_amount',
        transaction_time DATETIME PATH '$.transaction_time'
    )
) AS jt
WHERE DATE(jt.transaction_time) = CURDATE()
GROUP BY DATE(jt.transaction_time), jt.type;

-- 5. 使用函数查询库存
SELECT
    product_code,
    product_name,
    fn_get_stock(product_code) as current_stock,
    fn_can_stock_out(product_code, 5) as can_out_5_units
FROM inventory;

-- 6. 查看库存预警（库存小于安全库存）
SELECT
    product_code,
    product_name,
    current_stock,
    CASE
        WHEN current_stock = 0 THEN '缺货'
        WHEN current_stock < 10 THEN '库存不足'
        WHEN current_stock < 50 THEN '库存偏低'
        ELSE '库存正常'
    END as stock_status
FROM inventory
WHERE current_stock < 50
ORDER BY current_stock ASC;

-- 7. 商品交易统计
SELECT
    i.product_code,
    i.product_name,
    i.current_stock,
    COUNT(jt.transaction_id) as transaction_count,
    SUM(CASE WHEN jt.type = 'in' THEN jt.quantity ELSE 0 END) as total_in_qty,
    SUM(CASE WHEN jt.type = 'out' THEN jt.quantity ELSE 0 END) as total_out_qty,
    SUM(CASE WHEN jt.type = 'in' THEN jt.total_amount ELSE 0 END) as total_in_amount,
    SUM(CASE WHEN jt.type = 'out' THEN jt.total_amount ELSE 0 END) as total_out_amount
FROM inventory i
LEFT JOIN JSON_TABLE(
    i.transactions,
    '$[*]' COLUMNS (
        transaction_id VARCHAR(50) PATH '$.transaction_id',
        type VARCHAR(10) PATH '$.type',
        quantity DECIMAL(10,2) PATH '$.quantity',
        total_amount DECIMAL(10,2) PATH '$.total_amount'
    )
) AS jt ON 1=1
GROUP BY i.id, i.product_code, i.product_name, i.current_stock;

-- 8. 最近交易记录（最近10条）
SELECT
    i.product_code,
    i.product_name,
    jt.transaction_id,
    jt.type,
    jt.quantity,
    jt.price,
    jt.total_amount,
    jt.operator,
    jt.transaction_time
FROM inventory i
CROSS JOIN JSON_TABLE(
    i.transactions,
    '$[*]' COLUMNS (
        transaction_id VARCHAR(50) PATH '$.transaction_id',
        type VARCHAR(10) PATH '$.type',
        quantity DECIMAL(10,2) PATH '$.quantity',
        price DECIMAL(10,2) PATH '$.price',
        total_amount DECIMAL(10,2) PATH '$.total_amount',
        operator VARCHAR(50) PATH '$.operator',
        transaction_time DATETIME PATH '$.transaction_time'
    )
) AS jt
ORDER BY jt.transaction_time DESC
LIMIT 10;
