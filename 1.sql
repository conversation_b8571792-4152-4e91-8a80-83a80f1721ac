CREATE DEFINER=`bidata`@`%` PROCEDURE `AdjustDebitAmount`(
    IN p_voucher_date DATE -- 添加可选的凭证日期参数
)
BEGIN
    -- 定义变量
    DECLARE done INT DEFAULT 0;
    DECLARE cur_row_num INT;
    DECLARE cur_group_name VARCHAR(1024);
    DECLARE cur_project VARCHAR(255);
    DECLARE cur_subject VARCHAR(255);
    DECLARE cur_dept VARCHAR(255);
    DECLARE cur_customer VARCHAR(255);
    DECLARE cur_subcustomer VARCHAR(255);
    DECLARE cur_contract VARCHAR(255);
    DECLARE cur_voucher_date DATE;
    DECLARE cur_debit_amount DECIMAL(20, 2);
    DECLARE cur_credit_amount DECIMAL(20, 2);
    DECLARE cur_diff DECIMAL(20, 2);
    DECLARE cumulative_sum DECIMAL(20, 2) DEFAULT 0;
    DECLARE prev_group_name VARCHAR(1024) DEFAULT '';
    DECLARE opposite_row_num INT;

    -- 游标定义
    DECLARE cur CURSOR FOR
    SELECT `row_num`, `group_name`, `项目代码`, `科目代码`, `部门代码`, `客商代码`, `二级客商代码`, `合同项目代码`, `凭证日期`, `借方金额`,贷方金额, `debit_credit_diff`
    FROM temp_ordered order by group_name,row_num desc;

    -- 定义处理结束条件
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = 1;

    -- 清理临时表
    DROP TEMPORARY TABLE IF EXISTS temp_diff;
    DROP TEMPORARY TABLE IF EXISTS temp_ordered;

    -- 创建临时表来存储每个组的借方-贷方差值
    CREATE TEMPORARY TABLE temp_diff AS
    SELECT 
        `项目代码`, 
        `科目代码`,
        `部门代码`, 
        `客商代码`, 
        `二级客商代码`, 
        `合同项目代码`,
        SUM(`借方金额`) AS total_debit,
        SUM(`贷方金额`) AS total_credit,
        sum(COALESCE(借方金额, 0)) - sum(COALESCE(贷方金额, 0)) AS debit_credit_diff
    FROM XSZ_yszk 
    WHERE p_voucher_date IS NULL OR 凭证日期 <= p_voucher_date  -- 添加日期筛选条件
--   初始数据+临时凭证 = -1    and `凭证号` <> -1
    GROUP BY 
        `项目代码`,
        `科目代码`, 
        `部门代码`, 
        `客商代码`, 
        `二级客商代码`, 
        `合同项目代码` 
    HAVING debit_credit_diff <> 0;

    -- 创建临时表，按组筛选借方金额不为0的数据，并按凭证日期排序，生成组内行号
    CREATE TEMPORARY TABLE temp_ordered AS
    SELECT 
        ROW_NUMBER() OVER (PARTITION BY `项目代码`, `科目代码`, `部门代码`, `客商代码`, `二级客商代码`, `合同项目代码` ORDER BY `凭证日期`, `凭证号` DESC) AS row_num,
--         CONCAT(t1.`项目代码`, t1.`科目代码`, t1.`部门代码`, t1.`客商代码`, t1.`二级客商代码`, t1.`合同项目代码`) AS group_name,

CONCAT(t1.`项目代码`, t1.`科目代码`, COALESCE(t1.`部门代码`,'/') , t1.`客商代码`, COALESCE(t1.`二级客商代码`,'/'), COALESCE(t1.`合同项目代码`,'/')) as group_name,
        t1.*,
        (CASE
            WHEN (TIMESTAMPDIFF(MONTH, t1.凭证日期, COALESCE(p_voucher_date, CURDATE())) <= 12) THEN '1年以内'
            WHEN (TIMESTAMPDIFF(MONTH, t1.凭证日期, COALESCE(p_voucher_date, CURDATE())) BETWEEN 13 AND 24) THEN '1-2年'
            WHEN (TIMESTAMPDIFF(MONTH, t1.凭证日期, COALESCE(p_voucher_date, CURDATE())) BETWEEN 25 AND 36) THEN '2-3年'
            WHEN (TIMESTAMPDIFF(MONTH, t1.凭证日期, COALESCE(p_voucher_date, CURDATE())) BETWEEN 37 AND 48) THEN '3-4年'
            WHEN (TIMESTAMPDIFF(MONTH, t1.凭证日期, COALESCE(p_voucher_date, CURDATE())) BETWEEN 49 AND 60) THEN '4-5年'
            ELSE '5年以上'
        END) AS `账龄大类`,
        t2.debit_credit_diff,
        0 AS matched
    FROM XSZ_yszk t1
    JOIN temp_diff t2 ON t1.`项目代码` = t2.`项目代码`
                      AND t1.`科目代码` = t2.`科目代码`
                      AND t1.`部门代码` <=> t2.`部门代码`
                      AND t1.`客商代码` = t2.`客商代码`
                      AND t1.`二级客商代码` <=> t2.`二级客商代码`
                      AND t1.`合同项目代码` <=> t2.`合同项目代码`
    WHERE (p_voucher_date IS NULL OR t1.凭证日期 <= p_voucher_date)   -- 添加日期筛选条件
        AND ((COALESCE(t1.借方金额, 0) <> 0 and t2.debit_credit_diff>0) or t2.debit_credit_diff<0); 

    -- 清空结果表
    truncate table yszk;
    update temp_ordered set 借方金额 = (COALESCE(借方金额, 0) - COALESCE(贷方金额, 0));
    update temp_ordered set 贷方金额 = COALESCE(贷方金额, 0);
    insert into yszk SELECT * FROM temp_ordered;
    commit;

    -- 打开游标
    OPEN cur;
    read_loop: LOOP
        FETCH cur INTO cur_row_num, cur_group_name, cur_project, cur_subject, cur_dept, cur_customer, cur_subcustomer, cur_contract, cur_voucher_date, cur_debit_amount,cur_credit_amount, cur_diff;

        IF done THEN
            LEAVE read_loop;
        END IF;

        -- 跳过已经匹配的记录
        IF EXISTS (SELECT 1 FROM yszk WHERE row_num = cur_row_num and group_name = cur_group_name AND matched = 1) THEN
            ITERATE read_loop;
        END IF;

        -- 重置累计和针对每个组
        IF cur_group_name != prev_group_name THEN
            SET cumulative_sum = 0;
            SET prev_group_name = cur_group_name;
        END IF;
          -- 借贷方为整数，处理借方金额
          IF cur_diff > 0 then
            -- 判断累计和是否大于当前差值
            IF cumulative_sum + cur_debit_amount >= cur_diff  THEN
                -- 更新最后一条记录的借方金额
                UPDATE yszk
                SET `借方金额` = cur_diff - cumulative_sum
                -- ,差值余额 - 借方金额
                WHERE `row_num` = cur_row_num AND `group_name` = cur_group_name;
                -- 删除多余的记录
                delete from yszk  WHERE `row_num` < cur_row_num AND `group_name` = cur_group_name;
                -- 重置累计和和分组
                SET cumulative_sum = 0;
                SET prev_group_name = '';

            ELSE
                SET cumulative_sum = cumulative_sum + cur_debit_amount;
            END IF;
        ELSE
            ## 贷方有值，取消借方金额
            UPDATE yszk SET `借方金额` = 0 WHERE `row_num` = cur_row_num AND `group_name` = cur_group_name;
            -- 处理贷方金额
            IF cumulative_sum + cur_credit_amount >= abs(cur_diff)  THEN
                -- 更新最后一条记录的借方金额
                UPDATE yszk
                SET `贷方金额` = abs(cur_diff) - cumulative_sum
                -- ,差值余额 - 借方金额
                WHERE `row_num` = cur_row_num AND `group_name` = cur_group_name;
                -- 删除多余的记录
                delete from yszk  WHERE `row_num` < cur_row_num AND `group_name` = cur_group_name;
                -- 重置累计和和分组
                SET cumulative_sum = 0;
                SET prev_group_name = '';

            ELSE
                SET cumulative_sum = cumulative_sum + cur_credit_amount;
            END IF;          
        
        END if;
    END LOOP;

    -- 关闭游标
    CLOSE cur;

    -- 清理临时表和结果表中已匹配的记录
    DELETE FROM yszk WHERE matched = 1;
    delete from yszk where 借方金额=0;
    DROP TEMPORARY TABLE IF EXISTS temp_diff;
    DROP TEMPORARY TABLE IF EXISTS temp_ordered;
END