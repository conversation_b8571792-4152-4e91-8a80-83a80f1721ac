-- 三表分离的出入库管理系统设计
-- 库存表、入库明细表、出库明细表

CREATE DATABASE IF NOT EXISTS inventory_system_v2 
CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE inventory_system_v2;

-- 1. 库存主表
CREATE TABLE inventory (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    product_code VARCHAR(50) NOT NULL UNIQUE COMMENT '商品编码',
    product_name VARCHAR(200) NOT NULL COMMENT '商品名称',
    unit VARCHAR(20) NOT NULL DEFAULT '件' COMMENT '单位',
    current_stock DECIMAL(10,2) NOT NULL DEFAULT 0 COMMENT '当前库存数量',
    total_in DECIMAL(10,2) NOT NULL DEFAULT 0 COMMENT '累计入库数量',
    total_out DECIMAL(10,2) NOT NULL DEFAULT 0 COMMENT '累计出库数量',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 约束：库存不能为负数
    CONSTRAINT chk_current_stock CHECK (current_stock >= 0),
    CONSTRAINT chk_total_in CHECK (total_in >= 0),
    CONSTRAINT chk_total_out CHECK (total_out >= 0),
    
    -- 索引
    INDEX idx_product_code (product_code),
    INDEX idx_current_stock (current_stock)
) ENGINE=InnoDB COMMENT='库存主表';

-- 2. 入库明细表
CREATE TABLE stock_in_detail (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    transaction_id VARCHAR(50) NOT NULL UNIQUE COMMENT '入库单号',
    product_code VARCHAR(50) NOT NULL COMMENT '商品编码',
    quantity DECIMAL(10,2) NOT NULL COMMENT '入库数量',
    price DECIMAL(10,2) NOT NULL DEFAULT 0 COMMENT '入库单价',
    total_amount DECIMAL(10,2) AS (quantity * price) STORED COMMENT '入库总金额（计算列）',
    operator VARCHAR(50) NOT NULL COMMENT '操作员',
    remark VARCHAR(500) DEFAULT '' COMMENT '备注',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '入库时间',
    
    -- 约束
    CONSTRAINT chk_in_quantity CHECK (quantity > 0),
    CONSTRAINT chk_in_price CHECK (price >= 0),
    
    -- 外键约束
    CONSTRAINT fk_in_product_code FOREIGN KEY (product_code) REFERENCES inventory(product_code) ON UPDATE CASCADE,
    
    -- 索引
    INDEX idx_in_product_code (product_code),
    INDEX idx_in_transaction_id (transaction_id),
    INDEX idx_in_created_at (created_at)
) ENGINE=InnoDB COMMENT='入库明细表';

-- 3. 出库明细表
CREATE TABLE stock_out_detail (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    transaction_id VARCHAR(50) NOT NULL UNIQUE COMMENT '出库单号',
    product_code VARCHAR(50) NOT NULL COMMENT '商品编码',
    quantity DECIMAL(10,2) NOT NULL COMMENT '出库数量',
    price DECIMAL(10,2) NOT NULL DEFAULT 0 COMMENT '出库单价',
    total_amount DECIMAL(10,2) AS (quantity * price) STORED COMMENT '出库总金额（计算列）',
    operator VARCHAR(50) NOT NULL COMMENT '操作员',
    remark VARCHAR(500) DEFAULT '' COMMENT '备注',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '出库时间',
    
    -- 约束
    CONSTRAINT chk_out_quantity CHECK (quantity > 0),
    CONSTRAINT chk_out_price CHECK (price >= 0),
    
    -- 外键约束
    CONSTRAINT fk_out_product_code FOREIGN KEY (product_code) REFERENCES inventory(product_code) ON UPDATE CASCADE,
    
    -- 索引
    INDEX idx_out_product_code (product_code),
    INDEX idx_out_transaction_id (transaction_id),
    INDEX idx_out_created_at (created_at)
) ENGINE=InnoDB COMMENT='出库明细表';

-- 存储过程：入库操作（自动创建库存记录或更新）
DELIMITER //
CREATE PROCEDURE sp_stock_in_v2(
    IN p_product_code VARCHAR(50),
    IN p_product_name VARCHAR(200),
    IN p_unit VARCHAR(20),
    IN p_quantity DECIMAL(10,2),
    IN p_price DECIMAL(10,2),
    IN p_operator VARCHAR(50),
    IN p_remark VARCHAR(500)
)
BEGIN
    DECLARE v_transaction_id VARCHAR(50);
    DECLARE v_exists INT DEFAULT 0;
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    -- 生成入库单号
    SET v_transaction_id = CONCAT('IN', DATE_FORMAT(NOW(), '%Y%m%d%H%i%s'), LPAD(CONNECTION_ID(), 4, '0'));
    
    -- 检查商品是否存在
    SELECT COUNT(*) INTO v_exists FROM inventory WHERE product_code = p_product_code;
    
    -- 如果商品不存在，自动创建
    IF v_exists = 0 THEN
        INSERT INTO inventory (product_code, product_name, unit) 
        VALUES (p_product_code, p_product_name, COALESCE(p_unit, '件'));
    END IF;
    
    -- 插入入库明细
    INSERT INTO stock_in_detail (transaction_id, product_code, quantity, price, operator, remark)
    VALUES (v_transaction_id, p_product_code, p_quantity, p_price, p_operator, p_remark);
    
    -- 更新库存
    UPDATE inventory 
    SET 
        current_stock = current_stock + p_quantity,
        total_in = total_in + p_quantity,
        updated_at = NOW()
    WHERE product_code = p_product_code;
    
    COMMIT;
    
    -- 返回结果
    SELECT 
        v_transaction_id as transaction_id,
        p_product_code as product_code,
        'SUCCESS' as status,
        current_stock as new_stock
    FROM inventory 
    WHERE product_code = p_product_code;
END //

-- 存储过程：出库操作（检查库存）
CREATE PROCEDURE sp_stock_out_v2(
    IN p_product_code VARCHAR(50),
    IN p_quantity DECIMAL(10,2),
    IN p_price DECIMAL(10,2),
    IN p_operator VARCHAR(50),
    IN p_remark VARCHAR(500)
)
BEGIN
    DECLARE v_transaction_id VARCHAR(50);
    DECLARE v_current_stock DECIMAL(10,2) DEFAULT 0;
    DECLARE v_exists INT DEFAULT 0;
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    -- 检查商品是否存在并获取库存
    SELECT COUNT(*), COALESCE(MAX(current_stock), 0) 
    INTO v_exists, v_current_stock 
    FROM inventory 
    WHERE product_code = p_product_code;
    
    -- 商品不存在
    IF v_exists = 0 THEN
        ROLLBACK;
        SELECT 
            '' as transaction_id,
            p_product_code as product_code,
            'PRODUCT_NOT_EXISTS' as status,
            0 as current_stock,
            p_quantity as required_quantity;
    -- 库存不足
    ELSEIF v_current_stock < p_quantity THEN
        ROLLBACK;
        SELECT 
            '' as transaction_id,
            p_product_code as product_code,
            'INSUFFICIENT_STOCK' as status,
            v_current_stock as current_stock,
            p_quantity as required_quantity;
    ELSE
        -- 生成出库单号
        SET v_transaction_id = CONCAT('OUT', DATE_FORMAT(NOW(), '%Y%m%d%H%i%s'), LPAD(CONNECTION_ID(), 4, '0'));
        
        -- 插入出库明细
        INSERT INTO stock_out_detail (transaction_id, product_code, quantity, price, operator, remark)
        VALUES (v_transaction_id, p_product_code, p_quantity, p_price, p_operator, p_remark);
        
        -- 更新库存
        UPDATE inventory 
        SET 
            current_stock = current_stock - p_quantity,
            total_out = total_out + p_quantity,
            updated_at = NOW()
        WHERE product_code = p_product_code;
        
        COMMIT;
        
        -- 返回结果
        SELECT 
            v_transaction_id as transaction_id,
            p_product_code as product_code,
            'SUCCESS' as status,
            current_stock as new_stock
        FROM inventory 
        WHERE product_code = p_product_code;
    END IF;
END //
DELIMITER ;

-- 创建视图：库存汇总
CREATE VIEW v_inventory_summary AS
SELECT 
    i.product_code,
    i.product_name,
    i.unit,
    i.current_stock,
    i.total_in,
    i.total_out,
    COALESCE(in_count.transaction_count, 0) as in_transaction_count,
    COALESCE(out_count.transaction_count, 0) as out_transaction_count,
    COALESCE(in_amount.total_amount, 0) as total_in_amount,
    COALESCE(out_amount.total_amount, 0) as total_out_amount,
    i.created_at,
    i.updated_at
FROM inventory i
LEFT JOIN (
    SELECT product_code, COUNT(*) as transaction_count
    FROM stock_in_detail 
    GROUP BY product_code
) in_count ON i.product_code = in_count.product_code
LEFT JOIN (
    SELECT product_code, COUNT(*) as transaction_count
    FROM stock_out_detail 
    GROUP BY product_code
) out_count ON i.product_code = out_count.product_code
LEFT JOIN (
    SELECT product_code, SUM(total_amount) as total_amount
    FROM stock_in_detail 
    GROUP BY product_code
) in_amount ON i.product_code = in_amount.product_code
LEFT JOIN (
    SELECT product_code, SUM(total_amount) as total_amount
    FROM stock_out_detail 
    GROUP BY product_code
) out_amount ON i.product_code = out_amount.product_code;

-- 创建视图：所有交易记录
CREATE VIEW v_all_transactions AS
SELECT 
    'IN' as transaction_type,
    transaction_id,
    product_code,
    quantity,
    price,
    total_amount,
    operator,
    remark,
    created_at as transaction_time
FROM stock_in_detail
UNION ALL
SELECT 
    'OUT' as transaction_type,
    transaction_id,
    product_code,
    quantity,
    price,
    total_amount,
    operator,
    remark,
    created_at as transaction_time
FROM stock_out_detail
ORDER BY transaction_time DESC;

-- 示例数据和操作
-- 1. 入库操作（自动创建商品）
CALL sp_stock_in_v2('P001', '苹果手机', '台', 100, 5000.00, '张三', '首次入库');
CALL sp_stock_in_v2('P002', '笔记本电脑', '台', 50, 8000.00, '张三', '笔记本入库');

-- 2. 继续入库（商品已存在）
CALL sp_stock_in_v2('P001', '', '', 50, 5100.00, '李四', '补充库存');

-- 3. 出库操作
CALL sp_stock_out_v2('P001', 10, 5200.00, '王五', '销售出库');
CALL sp_stock_out_v2('P002', 5, 8500.00, '王五', '销售出库');

-- 4. 尝试超量出库（应该失败）
CALL sp_stock_out_v2('P001', 200, 5200.00, '赵六', '尝试超量出库');

-- 5. 尝试对不存在商品出库（应该失败）
CALL sp_stock_out_v2('P999', 10, 1000.00, '赵六', '不存在商品出库');

-- 查询示例
-- 1. 查看库存汇总
SELECT * FROM v_inventory_summary;

-- 2. 查看所有交易记录
SELECT * FROM v_all_transactions LIMIT 20;

-- 3. 查看库存不足商品
SELECT
    product_code,
    product_name,
    current_stock,
    CASE
        WHEN current_stock = 0 THEN '缺货'
        WHEN current_stock < 10 THEN '库存不足'
        WHEN current_stock < 50 THEN '库存偏低'
        ELSE '库存正常'
    END as stock_status
FROM inventory
WHERE current_stock < 50
ORDER BY current_stock ASC;

-- 4. 查看某商品的入库记录
SELECT
    transaction_id,
    quantity,
    price,
    total_amount,
    operator,
    remark,
    created_at
FROM stock_in_detail
WHERE product_code = 'P001'
ORDER BY created_at DESC;

-- 5. 查看某商品的出库记录
SELECT
    transaction_id,
    quantity,
    price,
    total_amount,
    operator,
    remark,
    created_at
FROM stock_out_detail
WHERE product_code = 'P001'
ORDER BY created_at DESC;

-- 6. 今日交易统计
SELECT
    transaction_type,
    COUNT(*) as transaction_count,
    SUM(quantity) as total_quantity,
    SUM(total_amount) as total_amount
FROM v_all_transactions
WHERE DATE(transaction_time) = CURDATE()
GROUP BY transaction_type;

-- 7. 商品交易汇总（按商品分组）
SELECT
    i.product_code,
    i.product_name,
    i.current_stock,
    COALESCE(in_stats.in_count, 0) as in_transaction_count,
    COALESCE(in_stats.in_quantity, 0) as total_in_quantity,
    COALESCE(in_stats.in_amount, 0) as total_in_amount,
    COALESCE(out_stats.out_count, 0) as out_transaction_count,
    COALESCE(out_stats.out_quantity, 0) as total_out_quantity,
    COALESCE(out_stats.out_amount, 0) as total_out_amount
FROM inventory i
LEFT JOIN (
    SELECT
        product_code,
        COUNT(*) as in_count,
        SUM(quantity) as in_quantity,
        SUM(total_amount) as in_amount
    FROM stock_in_detail
    GROUP BY product_code
) in_stats ON i.product_code = in_stats.product_code
LEFT JOIN (
    SELECT
        product_code,
        COUNT(*) as out_count,
        SUM(quantity) as out_quantity,
        SUM(total_amount) as out_amount
    FROM stock_out_detail
    GROUP BY product_code
) out_stats ON i.product_code = out_stats.product_code;
