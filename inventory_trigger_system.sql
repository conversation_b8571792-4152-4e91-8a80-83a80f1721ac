-- 基于触发器的出入库管理系统
-- 库存表（包含项目信息） + 流水表 + 触发器自动更新库存

CREATE DATABASE IF NOT EXISTS inventory_trigger_system 
CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE inventory_trigger_system;

-- 字典信息表
CREATE TABLE dict_info (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    type_id INT NOT NULL COMMENT '类型ID',
    value VARCHAR(200) NOT NULL COMMENT '字典值',
    status TINYINT NOT NULL DEFAULT 1 COMMENT '状态：1=有效，0=无效',
    remark VARCHAR(500) DEFAULT '' COMMENT '备注',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    INDEX idx_type_id (type_id),
    INDEX idx_value (value),
    INDEX idx_status (status),
    INDEX idx_type_value (type_id, value),
    INDEX idx_type_status (type_id, status)
) ENGINE=InnoDB COMMENT='字典信息表';

-- 1. 库存表（包含项目信息，主键为批次+项目+商品）
CREATE TABLE inventory (
    batch_time BIGINT NOT NULL COMMENT '入库批次时间戳',
    project_code VARCHAR(50) NOT NULL COMMENT '项目编码',
    product_code VARCHAR(50) NOT NULL COMMENT '商品编码',
    project_name VARCHAR(200) NOT NULL COMMENT '项目名称',
    product_name VARCHAR(200) NOT NULL COMMENT '商品名称',
    unit VARCHAR(20) NOT NULL DEFAULT '件' COMMENT '单位',
    current_stock DECIMAL(10,2) NOT NULL DEFAULT 0 COMMENT '当前库存数量',
    total_in DECIMAL(10,2) NOT NULL DEFAULT 0 COMMENT '累计入库数量',
    total_out DECIMAL(10,2) NOT NULL DEFAULT 0 COMMENT '累计出库数量',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    -- 联合主键：批次时间戳+项目编码+商品编码
    PRIMARY KEY (batch_time, project_code, product_code),

    -- 约束
    CONSTRAINT chk_current_stock CHECK (current_stock >= 0),
    CONSTRAINT chk_total_in CHECK (total_in >= 0),
    CONSTRAINT chk_total_out CHECK (total_out >= 0),

    -- 索引
    INDEX idx_project_code (project_code),
    INDEX idx_product_code (product_code),
    INDEX idx_current_stock (current_stock),
    INDEX idx_project_name (project_name),
    INDEX idx_batch_time (batch_time)
) ENGINE=InnoDB COMMENT='库存表';

-- 2. 流水表（出入库合并，增加批次时间戳）
CREATE TABLE stock_flow (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    flow_id VARCHAR(50) NOT NULL UNIQUE COMMENT '流水单号',
    batch_time BIGINT NOT NULL COMMENT '关联的库存批次时间戳',
    project_code VARCHAR(50) NOT NULL COMMENT '项目编码',
    product_code VARCHAR(50) NOT NULL COMMENT '商品编码',
    flow_type ENUM('IN', 'OUT') NOT NULL COMMENT '流水类型：IN=入库，OUT=出库',
    quantity DECIMAL(10,2) NOT NULL COMMENT '数量',
    price DECIMAL(10,2) NOT NULL DEFAULT 0 COMMENT '单价',
    total_amount DECIMAL(10,2) AS (quantity * price) STORED COMMENT '总金额（计算列）',
    operator VARCHAR(50) NOT NULL COMMENT '操作员',
    remark VARCHAR(500) DEFAULT '' COMMENT '备注',

    -- 审核状态：0=待审核，1=已审核，2=已拒绝
    audit_status TINYINT NOT NULL DEFAULT 0 COMMENT '审核状态：0=待审核，1=已审核，2=已拒绝',
    auditor VARCHAR(50) DEFAULT NULL COMMENT '审核员',
    audit_time TIMESTAMP NULL DEFAULT NULL COMMENT '审核时间',
    audit_remark VARCHAR(500) DEFAULT '' COMMENT '审核备注',

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    -- 约束
    CONSTRAINT chk_flow_quantity CHECK (quantity > 0),
    CONSTRAINT chk_flow_price CHECK (price >= 0),
    CONSTRAINT chk_audit_status CHECK (audit_status IN (0, 1, 2)),

    -- 索引优化
    INDEX idx_flow_batch_project_product (batch_time, project_code, product_code),
    INDEX idx_flow_project_product (project_code, product_code),
    INDEX idx_flow_type_audit (flow_type, audit_status),
    INDEX idx_audit_status_time (audit_status, audit_time),
    INDEX idx_created_at (created_at),
    INDEX idx_batch_time (batch_time),
    INDEX idx_operator (operator),
    -- 复合索引：用于自动驳回查询
    INDEX idx_batch_product_type_audit (batch_time, project_code, product_code, flow_type, audit_status),
    -- 复合索引：用于待审核流水查询
    INDEX idx_audit_type_time (audit_status, flow_type, created_at)
) ENGINE=InnoDB COMMENT='库存流水表';

-- 触发器：流水表更新时自动更新库存
DELIMITER //

-- 触发器：流水表插入后处理
CREATE TRIGGER tr_stock_flow_after_insert
AFTER INSERT ON stock_flow
FOR EACH ROW
BEGIN
    -- 只有审核状态为1时才更新库存
    IF NEW.audit_status = 1 THEN
        -- 检查库存记录是否存在
        IF NOT EXISTS (
            SELECT 1 FROM inventory
            WHERE batch_time = NEW.batch_time AND project_code = NEW.project_code AND product_code = NEW.product_code
        ) THEN
            -- 如果库存记录不存在，插入新记录（通常用于入库）
            IF NEW.flow_type = 'IN' THEN
                INSERT INTO inventory (
                    batch_time, project_code, project_name, product_code, product_name, unit,
                    current_stock, total_in, total_out
                ) VALUES (
                    NEW.batch_time,
                    NEW.project_code,
                    CONCAT('项目-', NEW.project_code), -- 默认项目名称
                    NEW.product_code,
                    CONCAT('商品-', NEW.product_code), -- 默认商品名称
                    '件',
                    NEW.quantity,
                    NEW.quantity,
                    0
                );

                -- 新增批次时，更新dict_info表
                INSERT INTO dict_info (type_id, value, status, remark)
                VALUES (999, CONCAT(NEW.batch_time, '-', NEW.project_code, '-', NEW.product_code), 1, '新增库存批次');
            END IF;
        ELSE
            -- 更新现有库存记录
            IF NEW.flow_type = 'IN' THEN
                UPDATE inventory
                SET
                    current_stock = current_stock + NEW.quantity,
                    total_in = total_in + NEW.quantity,
                    updated_at = NOW()
                WHERE batch_time = NEW.batch_time AND project_code = NEW.project_code AND product_code = NEW.product_code;
            ELSEIF NEW.flow_type = 'OUT' THEN
                UPDATE inventory
                SET
                    current_stock = current_stock - NEW.quantity,
                    total_out = total_out + NEW.quantity,
                    updated_at = NOW()
                WHERE batch_time = NEW.batch_time AND project_code = NEW.project_code AND product_code = NEW.product_code
                  AND current_stock >= NEW.quantity; -- 确保库存充足

                -- 检查库存是否减为0，如果是则更新dict_info状态并驳回其他待审核出库
                DECLARE v_current_stock DECIMAL(10,2);
                DECLARE v_batch_key VARCHAR(200);

                SELECT current_stock INTO v_current_stock
                FROM inventory
                WHERE batch_time = NEW.batch_time AND project_code = NEW.project_code AND product_code = NEW.product_code;

                IF v_current_stock = 0 THEN
                    SET v_batch_key = CONCAT(NEW.batch_time, '-', NEW.project_code, '-', NEW.product_code);

                    -- 使用索引优化的更新
                    UPDATE dict_info
                    SET status = 0, updated_at = NOW(), remark = '库存已空'
                    WHERE type_id = 999 AND value = v_batch_key;

                    -- 使用复合索引优化的自动驳回查询
                    UPDATE stock_flow
                    SET audit_status = 2,
                        auditor = 'SYSTEM',
                        audit_time = NOW(),
                        audit_remark = '库存已空，自动驳回'
                    WHERE batch_time = NEW.batch_time
                      AND project_code = NEW.project_code
                      AND product_code = NEW.product_code
                      AND flow_type = 'OUT'
                      AND audit_status = 0
                      AND id != NEW.id;
                END IF;
            END IF;
        END IF;
    END IF;
END //

-- 触发器：流水表更新时处理审核状态变化
CREATE TRIGGER tr_stock_flow_after_update
AFTER UPDATE ON stock_flow
FOR EACH ROW
BEGIN
    -- 当审核状态从非1变为1时，更新库存
    IF OLD.audit_status != 1 AND NEW.audit_status = 1 THEN
        -- 检查库存记录是否存在
        IF NOT EXISTS (
            SELECT 1 FROM inventory
            WHERE batch_time = NEW.batch_time AND project_code = NEW.project_code AND product_code = NEW.product_code
        ) THEN
            -- 如果库存记录不存在，插入新记录（通常用于入库）
            IF NEW.flow_type = 'IN' THEN
                INSERT INTO inventory (
                    batch_time, project_code, project_name, product_code, product_name, unit,
                    current_stock, total_in, total_out
                ) VALUES (
                    NEW.batch_time,
                    NEW.project_code,
                    CONCAT('项目-', NEW.project_code),
                    NEW.product_code,
                    CONCAT('商品-', NEW.product_code),
                    '件',
                    NEW.quantity,
                    NEW.quantity,
                    0
                );

                -- 新增批次时，更新dict_info表
                INSERT INTO dict_info (type_id, value, status, remark)
                VALUES (999, CONCAT(NEW.batch_time, '-', NEW.project_code, '-', NEW.product_code), 1, '新增库存批次');
            END IF;
        ELSE
            -- 更新现有库存记录
            IF NEW.flow_type = 'IN' THEN
                UPDATE inventory
                SET
                    current_stock = current_stock + NEW.quantity,
                    total_in = total_in + NEW.quantity,
                    updated_at = NOW()
                WHERE batch_time = NEW.batch_time AND project_code = NEW.project_code AND product_code = NEW.product_code;
            ELSEIF NEW.flow_type = 'OUT' THEN
                UPDATE inventory
                SET
                    current_stock = current_stock - NEW.quantity,
                    total_out = total_out + NEW.quantity,
                    updated_at = NOW()
                WHERE batch_time = NEW.batch_time AND project_code = NEW.project_code AND product_code = NEW.product_code
                  AND current_stock >= NEW.quantity; -- 确保库存充足

                -- 检查库存是否减为0，如果是则更新dict_info状态并驳回其他待审核出库
                DECLARE v_current_stock_upd DECIMAL(10,2);
                DECLARE v_batch_key_upd VARCHAR(200);

                SELECT current_stock INTO v_current_stock_upd
                FROM inventory
                WHERE batch_time = NEW.batch_time AND project_code = NEW.project_code AND product_code = NEW.product_code;

                IF v_current_stock_upd = 0 THEN
                    SET v_batch_key_upd = CONCAT(NEW.batch_time, '-', NEW.project_code, '-', NEW.product_code);

                    -- 使用索引优化的更新
                    UPDATE dict_info
                    SET status = 0, updated_at = NOW(), remark = '库存已空'
                    WHERE type_id = 999 AND value = v_batch_key_upd;

                    -- 使用复合索引优化的自动驳回查询
                    UPDATE stock_flow
                    SET audit_status = 2,
                        auditor = 'SYSTEM',
                        audit_time = NOW(),
                        audit_remark = '库存已空，自动驳回'
                    WHERE batch_time = NEW.batch_time
                      AND project_code = NEW.project_code
                      AND product_code = NEW.product_code
                      AND flow_type = 'OUT'
                      AND audit_status = 0
                      AND id != NEW.id;
                END IF;
            END IF;
        END IF;
    END IF;

    -- 当审核状态从1变为非1时，回滚库存变化
    IF OLD.audit_status = 1 AND NEW.audit_status != 1 THEN
        IF NEW.flow_type = 'IN' THEN
            UPDATE inventory
            SET
                current_stock = current_stock - NEW.quantity,
                total_in = total_in - NEW.quantity,
                updated_at = NOW()
            WHERE batch_time = NEW.batch_time AND project_code = NEW.project_code AND product_code = NEW.product_code
              AND current_stock >= NEW.quantity;
        ELSEIF NEW.flow_type = 'OUT' THEN
            UPDATE inventory
            SET
                current_stock = current_stock + NEW.quantity,
                total_out = total_out - NEW.quantity,
                updated_at = NOW()
            WHERE batch_time = NEW.batch_time AND project_code = NEW.project_code AND product_code = NEW.product_code;
        END IF;
    END IF;
END //

DELIMITER ;

-- 创建视图：库存汇总（按批次）
CREATE VIEW v_inventory_summary AS
SELECT
    i.batch_time,
    i.project_code,
    i.project_name,
    i.product_code,
    i.product_name,
    i.unit,
    i.current_stock,
    i.total_in,
    i.total_out,
    -- 待审核的入库数量
    COALESCE(pending_in.pending_quantity, 0) as pending_in_quantity,
    -- 待审核的出库数量
    COALESCE(pending_out.pending_quantity, 0) as pending_out_quantity,
    -- 最后交易时间
    COALESCE(last_flow.last_flow_time, i.created_at) as last_flow_time,
    -- 批次状态（从dict_info获取）
    COALESCE(dict.status, 1) as batch_status,
    i.created_at,
    i.updated_at
FROM inventory i
LEFT JOIN (
    SELECT batch_time, project_code, product_code, SUM(quantity) as pending_quantity
    FROM stock_flow
    WHERE flow_type = 'IN' AND audit_status = 0
    GROUP BY batch_time, project_code, product_code
) pending_in ON i.batch_time = pending_in.batch_time AND i.project_code = pending_in.project_code AND i.product_code = pending_in.product_code
LEFT JOIN (
    SELECT batch_time, project_code, product_code, SUM(quantity) as pending_quantity
    FROM stock_flow
    WHERE flow_type = 'OUT' AND audit_status = 0
    GROUP BY batch_time, project_code, product_code
) pending_out ON i.batch_time = pending_out.batch_time AND i.project_code = pending_out.project_code AND i.product_code = pending_out.product_code
LEFT JOIN (
    SELECT batch_time, project_code, product_code, MAX(created_at) as last_flow_time
    FROM stock_flow
    WHERE audit_status = 1
    GROUP BY batch_time, project_code, product_code
) last_flow ON i.batch_time = last_flow.batch_time AND i.project_code = last_flow.project_code AND i.product_code = last_flow.product_code
LEFT JOIN dict_info dict ON dict.type_id = 999 AND dict.value = CONCAT(i.batch_time, '-', i.project_code, '-', i.product_code);

-- 创建视图：商品库存汇总（所有批次合并）
CREATE VIEW v_product_stock_summary AS
SELECT
    i.project_code,
    i.project_name,
    i.product_code,
    i.product_name,
    i.unit,
    COUNT(*) as batch_count,
    SUM(i.current_stock) as total_current_stock,
    SUM(i.total_in) as total_in,
    SUM(i.total_out) as total_out,
    -- 待审核的入库数量
    COALESCE(pending_in.pending_quantity, 0) as pending_in_quantity,
    -- 待审核的出库数量
    COALESCE(pending_out.pending_quantity, 0) as pending_out_quantity,
    -- 最后交易时间
    MAX(COALESCE(last_flow.last_flow_time, i.created_at)) as last_flow_time,
    MIN(i.created_at) as first_created_at,
    MAX(i.updated_at) as last_updated_at
FROM inventory i
LEFT JOIN (
    SELECT project_code, product_code, SUM(quantity) as pending_quantity
    FROM stock_flow
    WHERE flow_type = 'IN' AND audit_status = 0
    GROUP BY project_code, product_code
) pending_in ON i.project_code = pending_in.project_code AND i.product_code = pending_in.product_code
LEFT JOIN (
    SELECT project_code, product_code, SUM(quantity) as pending_quantity
    FROM stock_flow
    WHERE flow_type = 'OUT' AND audit_status = 0
    GROUP BY project_code, product_code
) pending_out ON i.project_code = pending_out.project_code AND i.product_code = pending_out.product_code
LEFT JOIN (
    SELECT batch_time, project_code, product_code, MAX(created_at) as last_flow_time
    FROM stock_flow
    WHERE audit_status = 1
    GROUP BY batch_time, project_code, product_code
) last_flow ON i.batch_time = last_flow.batch_time AND i.project_code = last_flow.project_code AND i.product_code = last_flow.product_code
GROUP BY i.project_code, i.project_name, i.product_code, i.product_name, i.unit, pending_in.pending_quantity, pending_out.pending_quantity;

-- 创建视图：流水记录详情
CREATE VIEW v_flow_detail AS
SELECT
    sf.flow_id,
    sf.batch_time,
    sf.project_code,
    i.project_name,
    sf.product_code,
    i.product_name,
    sf.flow_type,
    sf.quantity,
    sf.price,
    sf.total_amount,
    sf.operator,
    sf.remark,
    CASE sf.audit_status
        WHEN 0 THEN '待审核'
        WHEN 1 THEN '已审核'
        WHEN 2 THEN '已拒绝'
    END as audit_status_text,
    sf.audit_status,
    sf.auditor,
    sf.audit_time,
    sf.audit_remark,
    -- 关联批次的当前库存
    COALESCE(i.current_stock, 0) as batch_current_stock,
    -- 批次状态
    COALESCE(dict.status, 1) as batch_status,
    CASE COALESCE(dict.status, 1)
        WHEN 1 THEN '有效'
        WHEN 0 THEN '已空'
        ELSE '未知'
    END as batch_status_text,
    sf.created_at,
    sf.updated_at
FROM stock_flow sf
LEFT JOIN inventory i ON sf.batch_time = i.batch_time AND sf.project_code = i.project_code AND sf.product_code = i.product_code
LEFT JOIN dict_info dict ON dict.type_id = 999 AND dict.value = CONCAT(sf.batch_time, '-', sf.project_code, '-', sf.product_code)
ORDER BY sf.created_at DESC;

-- 函数：检查指定批次库存是否充足
DELIMITER //
CREATE FUNCTION fn_check_batch_stock_sufficient(
    p_batch_time BIGINT,
    p_project_code VARCHAR(50),
    p_product_code VARCHAR(50),
    p_quantity DECIMAL(10,2)
) RETURNS BOOLEAN
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE v_current_stock DECIMAL(10,2) DEFAULT 0;

    SELECT COALESCE(current_stock, 0) INTO v_current_stock
    FROM inventory
    WHERE batch_time = p_batch_time AND project_code = p_project_code AND product_code = p_product_code;

    RETURN v_current_stock >= p_quantity;
END //

-- 函数：获取指定批次当前库存
CREATE FUNCTION fn_get_batch_current_stock(
    p_batch_time BIGINT,
    p_project_code VARCHAR(50),
    p_product_code VARCHAR(50)
) RETURNS DECIMAL(10,2)
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE v_current_stock DECIMAL(10,2) DEFAULT 0;

    SELECT COALESCE(current_stock, 0) INTO v_current_stock
    FROM inventory
    WHERE batch_time = p_batch_time AND project_code = p_project_code AND product_code = p_product_code;

    RETURN v_current_stock;
END //

-- 函数：获取商品总库存（所有批次汇总）
CREATE FUNCTION fn_get_total_stock(
    p_project_code VARCHAR(50),
    p_product_code VARCHAR(50)
) RETURNS DECIMAL(10,2)
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE v_total_stock DECIMAL(10,2) DEFAULT 0;

    SELECT COALESCE(SUM(current_stock), 0) INTO v_total_stock
    FROM inventory
    WHERE project_code = p_project_code AND product_code = p_product_code;

    RETURN v_total_stock;
END //
DELIMITER ;

-- 示例数据和操作
-- 1. 直接插入已审核的入库流水（触发器自动创建库存记录）
INSERT INTO stock_flow (
    flow_id, batch_time, project_code, product_code, flow_type, quantity, price,
    operator, remark, audit_status, auditor, audit_time
) VALUES
('IN20241219001', 1734595200000, 'PRJ001', 'P001', 'IN', 100, 5000.00, '张三', '首次入库', 1, '审核员A', NOW()),
('IN20241219002', 1734595260000, 'PRJ001', 'P002', 'IN', 50, 8000.00, '张三', '笔记本入库', 1, '审核员A', NOW()),
('IN20241219003', 1734595320000, 'PRJ002', 'P001', 'IN', 80, 5100.00, '李四', '零售店入库', 1, '审核员B', NOW());

-- 2. 手动更新库存记录的项目名称和商品名称
UPDATE inventory SET
    project_name = '电商项目',
    product_name = '苹果手机'
WHERE batch_time = 1734595200000 AND project_code = 'PRJ001' AND product_code = 'P001';

UPDATE inventory SET
    project_name = '电商项目',
    product_name = '笔记本电脑'
WHERE batch_time = 1734595260000 AND project_code = 'PRJ001' AND product_code = 'P002';

UPDATE inventory SET
    project_name = '零售项目',
    product_name = '苹果手机'
WHERE batch_time = 1734595320000 AND project_code = 'PRJ002' AND product_code = 'P001';

-- 3. 插入待审核的出库流水
INSERT INTO stock_flow (
    flow_id, batch_time, project_code, product_code, flow_type, quantity, price,
    operator, remark, audit_status
) VALUES
('OUT20241219001', 1734595200000, 'PRJ001', 'P001', 'OUT', 10, 5200.00, '王五', '销售出库', 0),
('OUT20241219002', 1734595260000, 'PRJ001', 'P002', 'OUT', 5, 8500.00, '王五', '销售出库', 0),
('OUT20241219003', 1734595200000, 'PRJ001', 'P001', 'OUT', 15, 5200.00, '赵六', '另一个出库申请', 0);

-- 4. 审核第一个出库流水（触发器自动更新库存）
UPDATE stock_flow
SET audit_status = 1, auditor = '审核员A', audit_time = NOW(), audit_remark = '审核通过'
WHERE flow_id = 'OUT20241219001';

-- 5. 审核第三个出库流水，这会导致库存清零并自动驳回其他待审核出库
UPDATE stock_flow
SET audit_status = 1, auditor = '审核员A', audit_time = NOW(), audit_remark = '审核通过'
WHERE flow_id = 'OUT20241219003';

-- 6. 插入超量出库流水（应该不会更新库存，因为库存不足）
INSERT INTO stock_flow (
    flow_id, batch_time, project_code, product_code, flow_type, quantity, price,
    operator, remark, audit_status, auditor, audit_time
) VALUES
('OUT20241219004', 1734595200000, 'PRJ001', 'P001', 'OUT', 200, 5200.00, '钱七', '尝试超量出库', 1, '审核员A', NOW());

-- 查询示例
-- 1. 查看库存汇总（按批次）
SELECT * FROM v_inventory_summary;

-- 2. 查看商品库存汇总（所有批次合并）
SELECT * FROM v_product_stock_summary;

-- 3. 查看流水记录详情
SELECT * FROM v_flow_detail LIMIT 20;

-- 4. 查看待审核的流水
SELECT * FROM v_flow_detail WHERE audit_status = 0;

-- 5. 查看已驳回的流水（库存已空自动驳回）
SELECT * FROM v_flow_detail WHERE audit_status = 2 AND audit_remark LIKE '%库存已空%';

-- 6. 使用函数检查库存
SELECT
    batch_time,
    project_code,
    product_code,
    fn_get_batch_current_stock(batch_time, project_code, product_code) as batch_stock,
    fn_get_total_stock(project_code, product_code) as total_stock,
    fn_check_batch_stock_sufficient(batch_time, project_code, product_code, 20) as can_out_20_from_batch
FROM inventory;

-- 7. 项目库存汇总（按批次）
SELECT
    project_code,
    project_name,
    COUNT(*) as batch_count,
    SUM(current_stock) as total_stock_quantity,
    SUM(pending_in_quantity) as total_pending_in,
    SUM(pending_out_quantity) as total_pending_out
FROM v_inventory_summary
GROUP BY project_code, project_name
ORDER BY project_code;

-- 8. 查看dict_info中的批次状态
SELECT
    type_id,
    value as batch_key,
    status,
    CASE status
        WHEN 1 THEN '有效'
        WHEN 0 THEN '已空'
        ELSE '未知'
    END as status_text,
    remark,
    created_at,
    updated_at
FROM dict_info
WHERE type_id = 999
ORDER BY created_at DESC;

-- 性能优化查询示例和分析

-- 9. 高效的待审核流水查询（使用复合索引）
SELECT
    flow_id, batch_time, project_code, product_code, flow_type,
    quantity, operator, created_at
FROM stock_flow
WHERE audit_status = 0 AND flow_type = 'OUT'
ORDER BY created_at DESC
LIMIT 100;

-- 10. 高效的批次库存查询（使用主键）
SELECT
    batch_time, project_code, product_code, product_name,
    current_stock, total_in, total_out
FROM inventory
WHERE batch_time = 1734595200000 AND project_code = 'PRJ001';

-- 11. 高效的项目库存汇总（避免全表扫描）
SELECT
    project_code,
    COUNT(*) as batch_count,
    SUM(current_stock) as total_stock,
    AVG(current_stock) as avg_stock_per_batch
FROM inventory
WHERE project_code = 'PRJ001'
GROUP BY project_code;

-- 12. 高效的dict_info批次状态查询
SELECT
    value as batch_key,
    status,
    remark,
    updated_at
FROM dict_info
WHERE type_id = 999 AND status = 0
ORDER BY updated_at DESC;

-- 13. 分页查询流水记录（大数据量优化）
SELECT
    flow_id, batch_time, project_code, product_code,
    flow_type, quantity, audit_status, created_at
FROM stock_flow
WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
  AND audit_status IN (0, 1)
ORDER BY created_at DESC
LIMIT 50 OFFSET 0;

-- 14. 库存预警查询（使用索引优化）
SELECT
    i.batch_time,
    i.project_code,
    i.product_code,
    i.product_name,
    i.current_stock,
    d.status as batch_status
FROM inventory i
LEFT JOIN dict_info d ON d.type_id = 999
    AND d.value = CONCAT(i.batch_time, '-', i.project_code, '-', i.product_code)
WHERE i.current_stock < 10 AND i.current_stock > 0
ORDER BY i.current_stock ASC, i.batch_time DESC;

-- 性能分析建议
/*
索引使用说明：

1. stock_flow表索引：
   - idx_batch_product_type_audit: 用于自动驳回查询，覆盖 (batch_time, project_code, product_code, flow_type, audit_status)
   - idx_audit_type_time: 用于待审核流水查询，覆盖 (audit_status, flow_type, created_at)
   - idx_flow_type_audit: 用于按类型和状态查询

2. inventory表索引：
   - 主键 (batch_time, project_code, product_code): 最高效的批次查询
   - idx_project_code: 用于项目级别汇总
   - idx_current_stock: 用于库存预警查询

3. dict_info表索引：
   - idx_type_value: 用于精确查找批次状态
   - idx_type_status: 用于按状态筛选批次

性能优化要点：
1. 避免在触发器中使用复杂子查询
2. 使用变量缓存重复查询结果
3. 复合索引覆盖常用查询条件组合
4. 分页查询避免大偏移量
5. 时间范围查询使用索引友好的条件
*/

-- 6. 库存预警查询
SELECT
    project_code,
    project_name,
    product_code,
    product_name,
    current_stock,
    CASE
        WHEN current_stock = 0 THEN '缺货'
        WHEN current_stock < 10 THEN '库存不足'
        WHEN current_stock < 50 THEN '库存偏低'
        ELSE '库存正常'
    END as stock_status
FROM v_inventory_summary
WHERE current_stock < 50
ORDER BY project_code, current_stock ASC;

-- 7. 流水统计报表
SELECT
    project_code,
    flow_type,
    audit_status,
    CASE audit_status
        WHEN 0 THEN '待审核'
        WHEN 1 THEN '已审核'
        WHEN 2 THEN '已拒绝'
    END as status_text,
    COUNT(*) as flow_count,
    SUM(quantity) as total_quantity,
    SUM(total_amount) as total_amount
FROM stock_flow
GROUP BY project_code, flow_type, audit_status
ORDER BY project_code, flow_type, audit_status;
