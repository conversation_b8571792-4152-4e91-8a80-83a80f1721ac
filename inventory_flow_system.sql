-- 基于流水表和项目分离的出入库管理系统
-- 库存表 + 流水表 + 审核机制

CREATE DATABASE IF NOT EXISTS inventory_flow_system 
CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE inventory_flow_system;

-- 1. 项目表
CREATE TABLE projects (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT '项目ID',
    project_code VARCHAR(50) NOT NULL UNIQUE COMMENT '项目编码',
    project_name VARCHAR(200) NOT NULL COMMENT '项目名称',
    description VARCHAR(500) DEFAULT '' COMMENT '项目描述',
    status TINYINT NOT NULL DEFAULT 1 COMMENT '项目状态：1=启用，0=禁用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_project_code (project_code),
    INDEX idx_status (status)
) ENGINE=InnoDB COMMENT='项目表';

-- 2. 库存表（按项目分离）
CREATE TABLE inventory (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    project_code VARCHAR(50) NOT NULL COMMENT '项目编码',
    product_code VARCHAR(50) NOT NULL COMMENT '商品编码',
    product_name VARCHAR(200) NOT NULL COMMENT '商品名称',
    unit VARCHAR(20) NOT NULL DEFAULT '件' COMMENT '单位',
    current_stock DECIMAL(10,2) NOT NULL DEFAULT 0 COMMENT '当前库存数量',
    total_in DECIMAL(10,2) NOT NULL DEFAULT 0 COMMENT '累计入库数量',
    total_out DECIMAL(10,2) NOT NULL DEFAULT 0 COMMENT '累计出库数量',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 约束
    CONSTRAINT chk_current_stock CHECK (current_stock >= 0),
    CONSTRAINT chk_total_in CHECK (total_in >= 0),
    CONSTRAINT chk_total_out CHECK (total_out >= 0),
    
    -- 外键约束
    CONSTRAINT fk_inventory_project FOREIGN KEY (project_code) REFERENCES projects(project_code) ON UPDATE CASCADE,
    
    -- 唯一约束：同一项目下商品编码唯一
    UNIQUE KEY uk_project_product (project_code, product_code),
    
    -- 索引
    INDEX idx_project_code (project_code),
    INDEX idx_product_code (product_code),
    INDEX idx_current_stock (current_stock)
) ENGINE=InnoDB COMMENT='库存表';

-- 3. 流水表（出入库合并）
CREATE TABLE stock_flow (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    flow_id VARCHAR(50) NOT NULL UNIQUE COMMENT '流水单号',
    project_code VARCHAR(50) NOT NULL COMMENT '项目编码',
    product_code VARCHAR(50) NOT NULL COMMENT '商品编码',
    flow_type ENUM('IN', 'OUT') NOT NULL COMMENT '流水类型：IN=入库，OUT=出库',
    quantity DECIMAL(10,2) NOT NULL COMMENT '数量',
    price DECIMAL(10,2) NOT NULL DEFAULT 0 COMMENT '单价',
    total_amount DECIMAL(10,2) AS (quantity * price) STORED COMMENT '总金额（计算列）',
    operator VARCHAR(50) NOT NULL COMMENT '操作员',
    remark VARCHAR(500) DEFAULT '' COMMENT '备注',
    
    -- 审核状态：0=待审核，1=已审核，2=已拒绝
    audit_status TINYINT NOT NULL DEFAULT 0 COMMENT '审核状态：0=待审核，1=已审核，2=已拒绝',
    auditor VARCHAR(50) DEFAULT NULL COMMENT '审核员',
    audit_time TIMESTAMP NULL DEFAULT NULL COMMENT '审核时间',
    audit_remark VARCHAR(500) DEFAULT '' COMMENT '审核备注',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 约束
    CONSTRAINT chk_flow_quantity CHECK (quantity > 0),
    CONSTRAINT chk_flow_price CHECK (price >= 0),
    CONSTRAINT chk_audit_status CHECK (audit_status IN (0, 1, 2)),
    
    -- 外键约束
    CONSTRAINT fk_flow_project FOREIGN KEY (project_code) REFERENCES projects(project_code) ON UPDATE CASCADE,
    
    -- 索引
    INDEX idx_flow_project_product (project_code, product_code),
    INDEX idx_flow_type (flow_type),
    INDEX idx_audit_status (audit_status),
    INDEX idx_created_at (created_at),
    INDEX idx_audit_time (audit_time)
) ENGINE=InnoDB COMMENT='库存流水表';

-- 存储过程：创建流水记录
DELIMITER //
CREATE PROCEDURE sp_create_flow(
    IN p_project_code VARCHAR(50),
    IN p_product_code VARCHAR(50),
    IN p_product_name VARCHAR(200),
    IN p_unit VARCHAR(20),
    IN p_flow_type ENUM('IN', 'OUT'),
    IN p_quantity DECIMAL(10,2),
    IN p_price DECIMAL(10,2),
    IN p_operator VARCHAR(50),
    IN p_remark VARCHAR(500)
)
BEGIN
    DECLARE v_flow_id VARCHAR(50);
    DECLARE v_project_exists INT DEFAULT 0;
    DECLARE v_inventory_exists INT DEFAULT 0;
    DECLARE v_current_stock DECIMAL(10,2) DEFAULT 0;
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    -- 检查项目是否存在且启用
    SELECT COUNT(*) INTO v_project_exists 
    FROM projects 
    WHERE project_code = p_project_code AND status = 1;
    
    IF v_project_exists = 0 THEN
        SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = '项目不存在或已禁用';
    END IF;
    
    -- 生成流水单号
    SET v_flow_id = CONCAT(p_flow_type, DATE_FORMAT(NOW(), '%Y%m%d%H%i%s'), LPAD(CONNECTION_ID(), 4, '0'));
    
    -- 检查库存记录是否存在
    SELECT COUNT(*), COALESCE(MAX(current_stock), 0) 
    INTO v_inventory_exists, v_current_stock
    FROM inventory 
    WHERE project_code = p_project_code AND product_code = p_product_code;
    
    -- 出库时检查库存
    IF p_flow_type = 'OUT' AND (v_inventory_exists = 0 OR v_current_stock < p_quantity) THEN
        SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = '库存不足，无法出库';
    END IF;
    
    -- 如果库存记录不存在且是入库，自动创建
    IF v_inventory_exists = 0 AND p_flow_type = 'IN' THEN
        INSERT INTO inventory (project_code, product_code, product_name, unit)
        VALUES (p_project_code, p_product_code, p_product_name, COALESCE(p_unit, '件'));
    END IF;
    
    -- 插入流水记录
    INSERT INTO stock_flow (
        flow_id, project_code, product_code, flow_type, 
        quantity, price, operator, remark
    ) VALUES (
        v_flow_id, p_project_code, p_product_code, p_flow_type,
        p_quantity, p_price, p_operator, p_remark
    );
    
    COMMIT;
    
    -- 返回结果
    SELECT 
        v_flow_id as flow_id,
        p_project_code as project_code,
        p_product_code as product_code,
        p_flow_type as flow_type,
        'CREATED' as status,
        '待审核' as audit_status
    FROM DUAL;
END //

-- 存储过程：审核流水记录
CREATE PROCEDURE sp_audit_flow(
    IN p_flow_id VARCHAR(50),
    IN p_audit_status TINYINT,
    IN p_auditor VARCHAR(50),
    IN p_audit_remark VARCHAR(500)
)
BEGIN
    DECLARE v_project_code VARCHAR(50);
    DECLARE v_product_code VARCHAR(50);
    DECLARE v_flow_type ENUM('IN', 'OUT');
    DECLARE v_quantity DECIMAL(10,2);
    DECLARE v_current_audit_status TINYINT;
    DECLARE v_current_stock DECIMAL(10,2) DEFAULT 0;
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    -- 获取流水记录信息
    SELECT project_code, product_code, flow_type, quantity, audit_status
    INTO v_project_code, v_product_code, v_flow_type, v_quantity, v_current_audit_status
    FROM stock_flow
    WHERE flow_id = p_flow_id;
    
    -- 检查流水记录是否存在
    IF v_project_code IS NULL THEN
        SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = '流水记录不存在';
    END IF;
    
    -- 检查是否已审核
    IF v_current_audit_status != 0 THEN
        SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = '该流水记录已审核，无法重复审核';
    END IF;
    
    -- 审核通过时，检查出库库存
    IF p_audit_status = 1 AND v_flow_type = 'OUT' THEN
        SELECT current_stock INTO v_current_stock
        FROM inventory
        WHERE project_code = v_project_code AND product_code = v_product_code;
        
        IF v_current_stock < v_quantity THEN
            SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = '当前库存不足，审核失败';
        END IF;
    END IF;
    
    -- 更新审核状态
    UPDATE stock_flow 
    SET 
        audit_status = p_audit_status,
        auditor = p_auditor,
        audit_time = NOW(),
        audit_remark = p_audit_remark,
        updated_at = NOW()
    WHERE flow_id = p_flow_id;
    
    -- 审核通过时更新库存
    IF p_audit_status = 1 THEN
        IF v_flow_type = 'IN' THEN
            UPDATE inventory 
            SET 
                current_stock = current_stock + v_quantity,
                total_in = total_in + v_quantity,
                updated_at = NOW()
            WHERE project_code = v_project_code AND product_code = v_product_code;
        ELSE -- OUT
            UPDATE inventory 
            SET 
                current_stock = current_stock - v_quantity,
                total_out = total_out + v_quantity,
                updated_at = NOW()
            WHERE project_code = v_project_code AND product_code = v_product_code;
        END IF;
    END IF;
    
    COMMIT;
    
    -- 返回结果
    SELECT 
        p_flow_id as flow_id,
        v_project_code as project_code,
        v_product_code as product_code,
        CASE p_audit_status 
            WHEN 1 THEN '审核通过' 
            WHEN 2 THEN '审核拒绝' 
            ELSE '未知状态' 
        END as audit_result,
        CASE 
            WHEN p_audit_status = 1 THEN 
                (SELECT current_stock FROM inventory 
                 WHERE project_code = v_project_code AND product_code = v_product_code)
            ELSE NULL 
        END as new_stock
    FROM DUAL;
END //
DELIMITER ;

-- 创建视图：库存汇总（按项目）
CREATE VIEW v_inventory_by_project AS
SELECT 
    p.project_code,
    p.project_name,
    i.product_code,
    i.product_name,
    i.unit,
    i.current_stock,
    i.total_in,
    i.total_out,
    -- 待审核的入库数量
    COALESCE(pending_in.pending_quantity, 0) as pending_in_quantity,
    -- 待审核的出库数量
    COALESCE(pending_out.pending_quantity, 0) as pending_out_quantity,
    i.created_at,
    i.updated_at
FROM projects p
LEFT JOIN inventory i ON p.project_code = i.project_code
LEFT JOIN (
    SELECT project_code, product_code, SUM(quantity) as pending_quantity
    FROM stock_flow 
    WHERE flow_type = 'IN' AND audit_status = 0
    GROUP BY project_code, product_code
) pending_in ON i.project_code = pending_in.project_code AND i.product_code = pending_in.product_code
LEFT JOIN (
    SELECT project_code, product_code, SUM(quantity) as pending_quantity
    FROM stock_flow 
    WHERE flow_type = 'OUT' AND audit_status = 0
    GROUP BY project_code, product_code
) pending_out ON i.project_code = pending_out.project_code AND i.product_code = pending_out.product_code
WHERE p.status = 1;

-- 创建视图：流水记录汇总
CREATE VIEW v_flow_summary AS
SELECT 
    sf.flow_id,
    p.project_name,
    sf.project_code,
    sf.product_code,
    i.product_name,
    sf.flow_type,
    sf.quantity,
    sf.price,
    sf.total_amount,
    sf.operator,
    sf.remark,
    CASE sf.audit_status 
        WHEN 0 THEN '待审核'
        WHEN 1 THEN '已审核'
        WHEN 2 THEN '已拒绝'
    END as audit_status_text,
    sf.audit_status,
    sf.auditor,
    sf.audit_time,
    sf.audit_remark,
    sf.created_at
FROM stock_flow sf
LEFT JOIN projects p ON sf.project_code = p.project_code
LEFT JOIN inventory i ON sf.project_code = i.project_code AND sf.product_code = i.product_code
ORDER BY sf.created_at DESC;

-- 示例数据
-- 1. 创建项目
INSERT INTO projects (project_code, project_name, description) VALUES
('PRJ001', '电商项目', '电商平台库存管理'),
('PRJ002', '零售项目', '线下零售店库存管理');

-- 2. 创建入库流水（待审核）
CALL sp_create_flow('PRJ001', 'P001', '苹果手机', '台', 'IN', 100, 5000.00, '张三', '首次入库');
CALL sp_create_flow('PRJ001', 'P002', '笔记本电脑', '台', 'IN', 50, 8000.00, '张三', '笔记本入库');
CALL sp_create_flow('PRJ002', 'P001', '苹果手机', '台', 'IN', 80, 5100.00, '李四', '零售店入库');

-- 3. 审核入库流水（需要替换实际的flow_id）
-- CALL sp_audit_flow('IN20241219123456001', 1, '审核员A', '审核通过');

-- 4. 创建出库流水（待审核）
-- CALL sp_create_flow('PRJ001', 'P001', '', '', 'OUT', 10, 5200.00, '王五', '销售出库');

-- 查询示例
-- 1. 查看所有项目的库存情况
SELECT * FROM v_inventory_by_project;

-- 2. 查看待审核的流水记录
SELECT * FROM v_flow_summary WHERE audit_status = 0;

-- 3. 查看已审核的流水记录
SELECT * FROM v_flow_summary WHERE audit_status = 1 LIMIT 20;

-- 4. 查看某项目的库存汇总
SELECT
    project_code,
    project_name,
    COUNT(*) as product_count,
    SUM(current_stock) as total_stock_value,
    SUM(pending_in_quantity) as total_pending_in,
    SUM(pending_out_quantity) as total_pending_out
FROM v_inventory_by_project
WHERE project_code = 'PRJ001'
GROUP BY project_code, project_name;

-- 5. 查看库存不足的商品（按项目）
SELECT
    project_code,
    project_name,
    product_code,
    product_name,
    current_stock,
    CASE
        WHEN current_stock = 0 THEN '缺货'
        WHEN current_stock < 10 THEN '库存不足'
        WHEN current_stock < 50 THEN '库存偏低'
        ELSE '库存正常'
    END as stock_status
FROM v_inventory_by_project
WHERE current_stock < 50 AND product_code IS NOT NULL
ORDER BY project_code, current_stock ASC;

-- 6. 审核统计报表
SELECT
    DATE(created_at) as flow_date,
    flow_type,
    audit_status,
    COUNT(*) as flow_count,
    SUM(quantity) as total_quantity,
    SUM(total_amount) as total_amount
FROM stock_flow
WHERE created_at >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
GROUP BY DATE(created_at), flow_type, audit_status
ORDER BY flow_date DESC, flow_type, audit_status;

-- 7. 项目库存价值统计
SELECT
    p.project_code,
    p.project_name,
    COUNT(DISTINCT i.product_code) as product_types,
    SUM(i.current_stock) as total_stock_quantity,
    -- 按最新入库价格估算库存价值
    SUM(i.current_stock * COALESCE(latest_price.price, 0)) as estimated_stock_value
FROM projects p
LEFT JOIN inventory i ON p.project_code = i.project_code
LEFT JOIN (
    SELECT
        project_code,
        product_code,
        price,
        ROW_NUMBER() OVER (PARTITION BY project_code, product_code ORDER BY created_at DESC) as rn
    FROM stock_flow
    WHERE flow_type = 'IN' AND audit_status = 1
) latest_price ON i.project_code = latest_price.project_code
                 AND i.product_code = latest_price.product_code
                 AND latest_price.rn = 1
WHERE p.status = 1
GROUP BY p.project_code, p.project_name
ORDER BY estimated_stock_value DESC;
